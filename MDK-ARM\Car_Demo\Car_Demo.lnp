--cpu=Cortex-M4.fp.sp
"car_demo\startup_stm32f407xx.o"
"car_demo\main.o"
"car_demo\gpio.o"
"car_demo\dma.o"
"car_demo\tim.o"
"car_demo\usart.o"
"car_demo\stm32f4xx_it.o"
"car_demo\stm32f4xx_hal_msp.o"
"car_demo\stm32f4xx_hal_tim.o"
"car_demo\stm32f4xx_hal_tim_ex.o"
"car_demo\stm32f4xx_hal_rcc.o"
"car_demo\stm32f4xx_hal_rcc_ex.o"
"car_demo\stm32f4xx_hal_flash.o"
"car_demo\stm32f4xx_hal_flash_ex.o"
"car_demo\stm32f4xx_hal_flash_ramfunc.o"
"car_demo\stm32f4xx_hal_gpio.o"
"car_demo\stm32f4xx_hal_dma_ex.o"
"car_demo\stm32f4xx_hal_dma.o"
"car_demo\stm32f4xx_hal_pwr.o"
"car_demo\stm32f4xx_hal_pwr_ex.o"
"car_demo\stm32f4xx_hal_cortex.o"
"car_demo\stm32f4xx_hal.o"
"car_demo\stm32f4xx_hal_exti.o"
"car_demo\stm32f4xx_hal_uart.o"
"car_demo\system_stm32f4xx.o"
"car_demo\ringbuffer.o"
"car_demo\uart_driver.o"
"car_demo\encoder_driver.o"
"car_demo\pid.o"
"car_demo\motor_driver.o"
"car_demo\hwt101_driver.o"
"car_demo\motor_app.o"
"car_demo\scheduler.o"
"car_demo\uart_app.o"
"car_demo\encoder_app.o"
--strict --scatter "Car_Demo\Car_Demo.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "Car_Demo.map" -o Car_Demo\Car_Demo.axf